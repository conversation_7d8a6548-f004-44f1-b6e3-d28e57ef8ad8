<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>
<ScrollPane xmlns:fx="http://javafx.com/fxml/1"
            xmlns="http://javafx.com/javafx/21"
            fx:controller="com.io661.extension.controller.PersonalSettingController"
            stylesheets="@../css/personalSetting.css">
   <content>
      <VBox spacing="20.0">
         <padding>
            <Insets bottom="30.0" left="30.0" right="30.0" top="30.0" />
         </padding>
         <children>
            <!-- 标题 -->
            <Label text="个人设置" styleClass="title-label">
               <font>
                  <Font size="24.0" />
               </font>
            </Label>

            <!-- 平台费率配置 -->
            <VBox spacing="15.0">
               <children>
                  <Label text="平台费率配置" styleClass="section-title">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>

                  <Label text="设置各平台的交易费率，用于计算到手价和上架价格" styleClass="description-label" />

                  <!-- 费率输入区域 -->
                  <GridPane hgap="20.0" vgap="15.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="100.0" prefWidth="120.0" />
                        <ColumnConstraints hgrow="NEVER" minWidth="20.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="100.0" prefWidth="120.0" />
                        <ColumnConstraints hgrow="NEVER" minWidth="20.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="200.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                     </rowConstraints>
                     <children>
                        <!-- 表头 -->
                        <Label text="平台" styleClass="section-title" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <Label text="交易费率" styleClass="section-title" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                        <Label text="提现费率" styleClass="section-title" GridPane.columnIndex="3" GridPane.rowIndex="0" />
                        <Label text="说明" styleClass="section-title" GridPane.columnIndex="5" GridPane.rowIndex="0" />

                        <!-- IO661平台 -->
                        <Label text="IO661:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                        <TextField fx:id="io661TransRateField" promptText="5.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                        <Label text="%" GridPane.columnIndex="2" GridPane.rowIndex="1" />
                        <TextField fx:id="io661WithdrawRateField" promptText="5.00" GridPane.columnIndex="3" GridPane.rowIndex="1" />
                        <Label text="%" GridPane.columnIndex="4" GridPane.rowIndex="1" />
                        <Label text="IO661平台的交易和提现费率" styleClass="help-label" GridPane.columnIndex="5" GridPane.rowIndex="1" />

                        <!-- 悠悠有品平台 -->
                        <Label text="悠悠有品:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                        <TextField fx:id="youPinTransRateField" promptText="10.00" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                        <Label text="%" GridPane.columnIndex="2" GridPane.rowIndex="2" />
                        <TextField fx:id="youPinWithdrawRateField" promptText="10.00" GridPane.columnIndex="3" GridPane.rowIndex="2" />
                        <Label text="%" GridPane.columnIndex="4" GridPane.rowIndex="2" />
                        <Label text="悠悠有品平台的交易和提现费率" styleClass="help-label" GridPane.columnIndex="5" GridPane.rowIndex="2" />
                     </children>
                  </GridPane>

                  <!-- 操作按钮 -->
                  <HBox spacing="10.0">
                     <children>
                        <Button fx:id="saveButton" onAction="#handleSave" text="保存配置" styleClass="primary-button" />
                        <Button fx:id="resetButton" onAction="#handleReset" text="重置默认" styleClass="secondary-button" />
                        <Region HBox.hgrow="ALWAYS" />
                        <Label fx:id="statusLabel" text="" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- 分隔线 -->
            <Separator />

            <!-- 价格计算测试 -->
            <VBox fx:id="testCalculationContainer" spacing="15.0">
               <children>
                  <Label text="价格计算测试" styleClass="section-title">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </Label>

                  <Label text="输入期望到手价，查看各平台需要设置的上架价格" styleClass="description-label" />

                  <GridPane hgap="15.0" vgap="10.0">
                     <columnConstraints>
                        <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="200.0" />
                        <ColumnConstraints hgrow="NEVER" minWidth="100.0" />
                        <ColumnConstraints hgrow="SOMETIMES" minWidth="150.0" prefWidth="200.0" />
                     </columnConstraints>
                     <rowConstraints>
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                        <RowConstraints minHeight="30.0" vgrow="NEVER" />
                     </rowConstraints>
                     <children>
                        <Label text="期望到手价:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                        <TextField fx:id="testReceivedPriceField" promptText="输入金额，如: 10.50" GridPane.columnIndex="1" GridPane.rowIndex="0" />

                        <Label text="选择平台:" GridPane.columnIndex="2" GridPane.rowIndex="0" />
                        <ComboBox fx:id="testPlatformComboBox" prefWidth="200.0" GridPane.columnIndex="3" GridPane.rowIndex="0" />

                        <Label fx:id="testListingPriceLabel" text="上架价格: --" styleClass="result-label" GridPane.columnIndex="1" GridPane.columnSpan="3" GridPane.rowIndex="1">
                           <font>
                              <Font size="16.0" />
                           </font>
                        </Label>
                     </children>
                  </GridPane>
               </children>
            </VBox>

            <!-- 说明信息 -->
            <VBox spacing="10.0">
               <children>
                  <Label text="使用说明" styleClass="section-title">
                     <font>
                        <Font size="16.0" />
                     </font>
                  </Label>
                  <Label text="• 费率设置：输入各平台的交易费率百分比（0-100之间）" styleClass="help-label" />
                  <Label text="• 到手价计算：系统会根据设置的费率自动计算需要设置的上架价格" styleClass="help-label" />
                  <Label text="• 计算公式：上架价格 = 到手价 ÷ (1 - 费率)" styleClass="help-label" />
                  <Label text="• 示例：期望到手价10元，费率5%，则上架价格 = 10 ÷ (1 - 0.05) = 10.53元" styleClass="help-label" />
               </children>
            </VBox>
         </children>
      </VBox>
   </content>
</ScrollPane>