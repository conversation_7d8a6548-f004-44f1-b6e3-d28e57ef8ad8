package com.io661.extension;

import com.io661.extension.model.User.PersonalSettings;
import com.io661.extension.service.Impl.PersonalSettingServiceImpl;
import com.io661.extension.service.PersonalSettingService;
import com.io661.extension.util.SQLite.SqliteManager;

/**
 * 测试个人设置功能
 */
public class TestPersonalSettings {
    public static void main(String[] args) {
        System.out.println("=== 测试个人设置功能 ===");
        
        try {
            // 初始化数据库连接
            SqliteManager.connectDatabase();
            System.out.println("数据库连接成功");
            
            // 创建服务实例
            PersonalSettingService service = new PersonalSettingServiceImpl();
            
            // 测试获取默认设置
            System.out.println("\n1. 测试获取默认设置:");
            PersonalSettings defaultSettings = service.getPersonalSettings();
            System.out.println("默认设置: " + defaultSettings);
            
            // 测试更新设置
            System.out.println("\n2. 测试更新设置:");
            PersonalSettings newSettings = new PersonalSettings();
            newSettings.setIo661TransRatio(0.06);
            newSettings.setIo661WithdrawRatio(0.06);
            newSettings.setYouPinTransRatio(0.12);
            newSettings.setYouPinWithdrawRatio(0.12);
            
            boolean updateSuccess = service.updatePersonalSettings(newSettings);
            System.out.println("更新结果: " + updateSuccess);
            
            // 验证更新后的设置
            System.out.println("\n3. 验证更新后的设置:");
            PersonalSettings updatedSettings = service.getPersonalSettings();
            System.out.println("更新后设置: " + updatedSettings);
            
            // 测试重置设置
            System.out.println("\n4. 测试重置设置:");
            boolean resetSuccess = service.resetPersonalSettings();
            System.out.println("重置结果: " + resetSuccess);
            
            // 验证重置后的设置
            System.out.println("\n5. 验证重置后的设置:");
            PersonalSettings resetSettings = service.getPersonalSettings();
            System.out.println("重置后设置: " + resetSettings);
            
            // 测试兼容性接口
            System.out.println("\n6. 测试兼容性接口:");
            var platformFeeRates = service.getPlatformFeeRates();
            System.out.println("平台费率映射: " + platformFeeRates);
            
            // 测试设置单个平台费率
            System.out.println("\n7. 测试设置单个平台费率:");
            boolean setPlatformSuccess = service.setPlatformFeeRate("io661", 0.08);
            System.out.println("设置IO661费率结果: " + setPlatformSuccess);
            
            // 验证单个平台费率设置
            PersonalSettings finalSettings = service.getPersonalSettings();
            System.out.println("最终设置: " + finalSettings);
            
            System.out.println("\n=== 测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
